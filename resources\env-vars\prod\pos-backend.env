PORT=3000
NODE_ENV=production
HOST=0.0.0.0
POSTGRES_URI=postgresql://postgres:<EMAIL>:5432/zithara_prod
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=JAbgRh70yiSiS3NthMOLiEO15Y7NNRKK14A5/8r2
COGNITO_USERPOOL_ID=ap-south-1_0d8vEd6iV
AUTH_URL=https://zithara-pos-prod.auth.ap-south-1.amazoncognito.com/oauth2/token
SHOPIFY_SETTING_TABLE=shopify-settings-prod


POSTGRES_URI_DATABASE=zithara_prod
POSTGRES_URI_HOST=zithara-prod.cvoklswabld9.ap-south-1.rds.amazonaws.com
POSTGRES_URI_USERNAME=postgres
POSTGRES_URI_PASSWORD=L2lGrME6RKoW

CAMPAIGN_BACKEND_URL=https://api-campaign.zithara.com/v1

KALASHAUSERNAME=S1W6E2cUB0lFGAKw
KALASHAPASSWORD=veiDSYzXJ7imela6m7d1apWLqhr6RbCn
KALASHACOMPANYCODE=S0FMQVNIQQ==
KALASHAURL=https://prodone.jewelflow.pro/kalasha/zithara/Zithara_api

BUGSNAG_API_KEY=b91979e665a36841024dbaaa44cf59b8
BUGSNAG_NOTIFY=true

SEQUELIZE_LOGGING=false