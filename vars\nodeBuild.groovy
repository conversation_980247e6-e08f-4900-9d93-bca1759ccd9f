#!/usr/bin/env groovy
def call(Map pipelineArgs) {
  pipeline {
    agent {
      label 'worker'
    }
    options {
      timestamps()
      disableConcurrentBuilds()
      timeout(time: 20, unit: 'MINUTES')
    }
    environment {
      DOCKER_REGISTRY_ORG = utility.getECRName()
      APP_NAME = "${JOB_BASE_NAME}"
      BRANCH_NAME = "${BRANCH}"
      IMAGE = utility.getNodeImage(pipelineArgs.image,env.JOB_BASE_NAME)
      TAG = "dev"
    }
    stages {
      stage('Git Checkout') {
        steps {
          checkout scm
          sh "rm Dockerfile || true"
          sh "rm .env || true"
        }
      }
      stage('Release Tag') {
        when {
          expression {
            return env.BRANCH.contains('wip');
          }
        }
        steps {
          script {
            I_TAG = tagCreation(env.JOB_NAME, env.VERSION, 'get', env.PVERSION)
          }
        }
      }
      stage('Production Build') {
        when {
          expression {
            return env.BRANCH.contains('main') || env.BRANCH.contains('master') || env.BRANCH.contains('newrelic');
          }
        }
        environment {
          TAG = "prod"
        }
        steps {
          script {
            utility.createSshfile(env.JOB_BASE_NAME)
            utility.createDockerfile(env.JOB_BASE_NAME)
            utility.createEnvfile("prod",env.JOB_BASE_NAME)
          }
          sh "eval \$(aws ecr get-login --no-include-email --region ap-south-1) && sleep 2"
          sh "aws ecr create-repository --repository-name \${APP_NAME} --region ap-south-1 || true"
          sh "docker build . -f NodeDockerfile --build-arg ENV_NAME=\${TAG} --build-arg IMAGE=\${IMAGE} --build-arg APP_NAME=\${APP_NAME} --build-arg GIT_COMMIT=\${GIT_COMMIT} -t \${DOCKER_REGISTRY_ORG}/\${APP_NAME}:\${TAG}"
          sh "docker push \${DOCKER_REGISTRY_ORG}/\${APP_NAME}:\${TAG}"
          sh "docker rmi \${DOCKER_REGISTRY_ORG}/\${APP_NAME}:\${TAG}"
        }
      }
      stage('Production Deploy') {
        when {
          expression {
            return env.BRANCH.contains('main') || env.BRANCH.contains('master') || env.BRANCH.contains('newrelic');
          }
        }
        environment {
          TAG = "prod"
          SSH = "ssh -tt -i ~/.ssh/zithara.pem ubuntu@10.100.2.223"
        }
        steps {
          sh "$SSH 'aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 153865421975.dkr.ecr.ap-south-1.amazonaws.com; cd /home/<USER>/docker;docker compose pull; docker rm -f $JOB_BASE_NAME || true; COMPOSE_HTTP_TIMEOUT=200 docker compose up -d'"
        }
      }
      stage('Development Build') {
        when {
          expression {
            return !(env.BRANCH.contains('main')) && !(env.BRANCH.contains('master')) && !(env.BRANCH.contains('newrelic'));
          }
        }
        steps {
          script {
            utility.createSshfile(env.JOB_BASE_NAME)
            utility.createDockerfile(env.JOB_BASE_NAME)
            utility.createEnvfile("dev",env.JOB_BASE_NAME)
          }
          sh "eval \$(aws ecr get-login --no-include-email --region ap-south-1) && sleep 2"
          sh "aws ecr create-repository --repository-name \${APP_NAME} --region ap-south-1 || true"
          sh "docker build . --memory=4g --memory-swap=6g -f NodeDockerfile --build-arg ENV_NAME=\${TAG} --build-arg IMAGE=\${IMAGE} --build-arg APP_NAME=\${APP_NAME} --build-arg GIT_COMMIT=\${GIT_COMMIT} -t \${DOCKER_REGISTRY_ORG}/\${APP_NAME}:\${TAG}"
          sh "docker push \${DOCKER_REGISTRY_ORG}/\${APP_NAME}:\${TAG}"
          sh "docker rmi \${DOCKER_REGISTRY_ORG}/\${APP_NAME}:\${TAG}"
        }
      }

      stage('Development Deploy') {
        when {
          expression {
            return !(env.BRANCH.contains('main')) && !(env.BRANCH.contains('master')) && !(env.BRANCH.contains('newrelic'));
          }
        }
        environment {
          SSH = "ssh -tt -i ~/.ssh/zithara.pem ubuntu@10.100.1.184"
        }
        steps {
          sh "$SSH 'aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 153865421975.dkr.ecr.ap-south-1.amazonaws.com; cd /home/<USER>/docker;docker compose pull; docker rm -f $JOB_BASE_NAME || true; COMPOSE_HTTP_TIMEOUT=200 docker compose up -d'"
        }
      }

      stage('Archive Artifacts') {
        steps {
          sh "echo APP_NAME = ${APP_NAME} > build.properties"
          sh "echo BRANCH_NAME = ${BRANCH} >> build.properties"
          sh "echo BUILD_NUMBER = ${BUILD_NUMBER} >> build.properties"
          sh "echo BUILD_URL = ${BUILD_URL} >> build.properties"
          sh "echo GIT_COMMIT = ${GIT_COMMIT} >> build.properties"
          archiveArtifacts 'build.properties'
        }

      }
    }

    post {
      always {
        echo "Cleaning up ${WORKSPACE}"
        // clean up our workspace 
        deleteDir()
        // clean up tmp directory 
        dir("${workspace}@tmp") {
          deleteDir()
        }
      }
    }
  }
}