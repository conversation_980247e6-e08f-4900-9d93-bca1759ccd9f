#!/usr/bin/env groovy
def call(Map pipelineArgs) {
  pipeline {
    agent {
      label 'worker'
    }

    environment {
        // Determine ENV based on branch name
        ENV = "${env.BRANCH_NAME == 'main' ? 'prod' : 'dev'}"
        // S3 bucket name based on environment
        S3_BUCKET = "zithara-${ENV}-tracking-js"
        // CloudFront Distribution IDs for dev and prod
        CF_DEV_DISTRIBUTION_ID = 'E3GUO16MPC9UT1'
        CF_PROD_DISTRIBUTION_ID = 'E14ELYVEVG5HTB'
        // Path to the script.js file
        FILE_PATH = "script.js"
    }

    stages {
        stage('Upload to S3') {
            steps {
                script {
                    echo "Uploading ${FILE_PATH} to S3 bucket ${S3_BUCKET}..."
                    // Upload file to the appropriate S3 bucket
                    sh """
                        aws s3 cp ${FILE_PATH} s3://${S3_BUCKET}/
                    """
                }
            }
        }

        stage('Invalidate CloudFront Cache') {
            steps {
                script {
                    def cfDistributionId = (ENV == 'dev') ? CF_DEV_DISTRIBUTION_ID : CF_PROD_DISTRIBUTION_ID
                    echo "Invalidating CloudFront distribution cache for ID ${cfDistributionId}..."
                    // Invalidate CloudFront cache based on the environment
                    sh "aws cloudfront create-invalidation --distribution-id ${cfDistributionId} --paths '/*'"
                }
            }
        }
    }

    post {
        always {
            echo "Pipeline execution completed."
        }
        failure {
            echo "Pipeline failed."
        }
        success {
            echo "Pipeline succeeded."
        }
    }
}
}
