#!/usr/bin/env groovy
def getECRName() {
  String repo = steps.sh(returnStdout: true, script: "aws ecr get-login --region ap-south-1 --no-include-email | cut -d \" \" -f 7").trim()
  return repo.replace("https://", "")
}
def createEnvfile(env,app) {
  def content = libraryResource "env-vars/${env}/${app}.env"
  println('\tCreating env file...')
  writeFile file: '.env',
  text: content
  if (app == 'ecommerce-backend') {
    println('\tCreating Again...')
    writeFile file: 'env-vars',
    text: content
  }
}
def getNodeImage(image,app) {
  if (image != null) {
    return image
  }
  if (app == 'campaign-backend') {
    return "153865421975.dkr.ecr.ap-south-1.amazonaws.com/node:20"
  }
  return "153865421975.dkr.ecr.ap-south-1.amazonaws.com/node:18"
}
def getS3Image(image,app) {
  if (image != null) {
    return image
  }
  if (app == 'different') {
    return "node:20"
  }
  return "node:18"
}

def createS3Dockerfile(app) {
  def dockerfile = libraryResource 'Dockerfile.s3'
    if (app == 'different') {
      dockerfile = libraryResource 'Dockerfile.s3.differentcdn'
    }
  writeFile file: 'S3Dockerfile',
  text: dockerfile
}

def createSshfile(app) {
  def id_rsa = libraryResource 'Dockerfile.id_rsa'
    if (app == 'different') {
      dockerfile = libraryResource 'Dockerfile.id_rsa.differentcdn'
    }
  writeFile file: 'id_rsa',
  text: id_rsa
}

def createDockerfile(app) {
  if (fileExists('build.gradle')) {
    def dockerfile = libraryResource 'Dockerfile.gradle'
    println('Identified as Gradle project...')
    if (fileExists('JavaDockerfile')) {
      println('\tDockerfile exists not creating...')
    }
    else {
      writeFile file: 'JavaDockerfile',
      text: dockerfile
    }
  }
  else if (fileExists('server.php')) {
    def dockerfile = libraryResource 'Dockerfile.php'
    println('Identified as php project...')
    if (fileExists('PhpDockerfile')) {
      println('\tDockerfile exists not creating...')
    }
    else {
      writeFile file: 'PhpDockerfile',
      text: dockerfile
    }
  }

  else if (fileExists('package.json')) {
    def dockerfile = libraryResource 'Dockerfile.Buildnode'
    println('Identified as Node project...')
    if (app == "ecommerce-backend" || app == "campaign-backend" || app == "pos-backend"){
      dockerfile = libraryResource 'Dockerfile.node'
    }
    if (fileExists('NodeDockerfile')) {
      println('\tDockerfile exists not creating...')
    }    
    else {
      writeFile file: 'NodeDockerfile',
      text: dockerfile
    }
  }
  else if (fileExists('requirement.txt') || fileExists('requirements.txt')) {
    def dockerfile = libraryResource 'Dockerfile.python'
    println('Identified as python project...')
    if (fileExists('PythonDockerfile')) {
      println('\tDockerfile exists not creating...')
    }
    else {
      writeFile file: 'PythonDockerfile',
      text: dockerfile
    }
  }
  else {
    println('Unable to identify build type, not creating dockerfile...')
  }
}