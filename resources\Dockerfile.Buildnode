FROM node:20
ARG APP_NAME
ARG ENV_NAME
LABEL org.opencontainers.image.authors="<EMAIL>"
WORKDIR /usr/src/app
COPY package*.json ./
#RUN npm config set registry https://registry.npmmirror.com/ --global
RUN npm config set registry https://registry.npmjs.org/ --global
RUN rm -rf node_modules package-lock.json
RUN npm cache clean --force
RUN npm cache verify
RUN npm install --force
COPY . .
RUN if [ "$APP_NAME" != "campaigns-frontend" ] && [ "$APP_NAME" != "socket-service" ] && [ "$APP_NAME" != "admin-panel-backend" ]; then npm run build;fi
RUN if [ "$APP_NAME" = "campaigns-frontend" ]; then \
      start_time=$(date +%s) && \
      npm run build && \
      end_time=$(date +%s) && \
      elapsed_time=$((end_time - start_time)) && \
      if [ $elapsed_time -lt 100 ]; then \
        echo "Build completed too quickly: ${elapsed_time} seconds. Minimum required: 100 seconds." && exit 1; \
      fi; \
    fi
ARG GIT_COMMIT
RUN echo ${GIT_COMMIT} > /home/<USER>
CMD ["node", "src/index.js", "--host 0.0.0.0"]
