#!/usr/bin/env groovy
def call(Map pipelineArgs) {
  pipeline {
    agent {
      label 'worker'
    }
    options {
      timestamps()
      disableConcurrentBuilds()
      timeout(time: 20, unit: 'MINUTES')
    }
    environment {
      DOCKER_REGISTRY_ORG = utility.getECRName()
      APP_NAME = "${JOB_BASE_NAME}"
      BRANCH_NAME = "${BRANCH}"
      IMAGE = utility.getS3Image(pipelineArgs.image,env.JOB_BASE_NAME)
      TAG = "dev"
    }
    stages {
      stage('Git Checkout') {
        steps {
          /*
          wrap([$class: 'BuildUser']) {
            sendNotification('STARTED', env.BUILD_USER, env.BRANCH, 'build', env.JOB_NAME)
            script {
              USER = utility.sendUserName(env.BUILD_USER)
            }
          }
          */
          checkout scm
          sh "rm Dockerfile || true"
        }
      }
      stage('Release Tag') {
        when {
          expression {
            return env.BRANCH.equals('wip');
          }
        }
        steps {
          script {
            I_TAG = tagCreation(env.JOB_NAME, env.VERSION, 'get', env.PVERSION)
          }
        }
      }

      stage('Production Build') {
        when {
          expression {
            return env.BRANCH.contains('main') || env.BRANCH.contains('master');
          }
        }
        environment {
          TAG = "prod"
        }
        steps {
          script {
            utility.createS3Dockerfile(env.JOB_BASE_NAME)
          }
          sh "aws s3api create-bucket --bucket zithara-prod-\${APP_NAME} --region ap-south-1 --create-bucket-configuration LocationConstraint=ap-south-1 || true"
          sh "git archive --remote=ssh://*****************/zithararewards/zithara-cicd-repo.git HEAD resources/env-vars/\${TAG}/\${APP_NAME}.env | tar -x;"
          sh "mv resources/env-vars/\${TAG}/\${APP_NAME}.env .env;"
          sh "eval \$(aws ecr get-login --no-include-email --region ap-south-1) && sleep 2"
          sh "docker build . -f S3Dockerfile --build-arg IMAGE=\${IMAGE} --build-arg APP_NAME=\${APP_NAME} --build-arg NODE_ENV=prod --build-arg TAG=\${TAG} --build-arg BUCKET_ARN=s3://zithara-prod-\${APP_NAME}/"
          sh "aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_PROD_ID --paths '/*' || true"
          //sendNotification('TAG_RELEASED', env.BUILD_USER, env.BRANCH, env.TAG, env.JOB_NAME)

        }
      }

      stage('Development Build') {
        when {
          expression {
            return !(env.BRANCH.contains('main')) && !(env.BRANCH.contains('master'));
          }
        }
        steps {
          script {
            utility.createS3Dockerfile(env.JOB_BASE_NAME)
          }
          sh "aws s3api create-bucket --bucket zithara-dev-\${APP_NAME} --region ap-south-1 --create-bucket-configuration LocationConstraint=ap-south-1 || true"
          sh "git archive --remote=ssh://*****************/zithararewards/zithara-cicd-repo.git HEAD resources/env-vars/\${TAG}/\${APP_NAME}.env | tar -x;"
          sh "mv resources/env-vars/\${TAG}/\${APP_NAME}.env .env;"
          sh "eval \$(aws ecr get-login --no-include-email --region ap-south-1) && sleep 2"
          sh "docker build . -f S3Dockerfile --build-arg IMAGE=\${IMAGE} --build-arg APP_NAME=\${APP_NAME} --build-arg NODE_ENV=dev --build-arg TAG=\${TAG} --build-arg BUCKET_ARN=s3://zithara-dev-\${APP_NAME}/"
          sh "aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DEV_ID --paths '/*' || true"
        }
      }

      stage('Archive Artifacts') {
        steps {
          sh "echo APP_NAME = ${APP_NAME} > build.properties"
          sh "echo BRANCH_NAME = ${BRANCH} >> build.properties"
          sh "echo BUILD_NUMBER = ${BUILD_NUMBER} >> build.properties"
          sh "echo BUILD_URL = ${BUILD_URL} >> build.properties"
          sh "echo GIT_COMMIT = ${GIT_COMMIT} >> build.properties"
          archiveArtifacts 'build.properties'
        }

      }
    }

    post {
      always {
        echo "Cleaning up ${WORKSPACE}"
        // clean up our workspace 
        deleteDir()
        // clean up tmp directory 
        dir("${workspace}@tmp") {
          deleteDir()
        }
        //wrap([$class: 'BuildUser']) {
        //  sendNotification(currentBuild.result, env.BUILD_USER, env.BRANCH, 'build', env.JOB_NAME)
        //}
      }
    }
  }
}