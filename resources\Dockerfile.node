ARG IMAGE
FROM $IMAGE
ARG APP_NAME
ARG ENV_NAME
LABEL org.opencontainers.image.authors="<EMAIL>"
WORKDIR /usr/src/app
COPY package*.json ./

RUN npm install
COPY . .
ENV NEW_RELIC_NO_CONFIG_FILE=true
ENV NEW_RELIC_DISTRIBUTED_TRACING_ENABLED=true
ENV NEW_RELIC_LOG=stdout

RUN if [ "$APP_NAME" = "ecommerce-backend" ] && [ "$ENV_NAME" = "dev" ]; then npm run build;fi
RUN if [ "$APP_NAME" = "ecommerce-backend" ] && [ "$ENV_NAME" = "prod" ]; then npm i @adonisjs/ace && node ace build --production --ignore-ts-errors;fi
RUN if [ "$APP_NAME" = "ecommerce-backend" ]; then cp env-vars build/.env;fi
ARG GIT_COMMIT
RUN echo ${GIT_COMMIT} > /home/<USER>
CMD ["node", "src/index.js", "--host 0.0.0.0"]